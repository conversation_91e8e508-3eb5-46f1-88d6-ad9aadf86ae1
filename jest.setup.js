import '@testing-library/jest-dom'

// Mock next/navigation
jest.mock('next/navigation', () => ({
  useRouter() {
    return {
      push: jest.fn(),
      replace: jest.fn(),
      prefetch: jest.fn(),
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
    }
  },
  useSearchParams() {
    return new URLSearchParams()
  },
  usePathname() {
    return ''
  },
}))

// Mock fetch globally
global.fetch = jest.fn()

// Mock Next.js Request and Response for API route testing
global.Request = jest.fn().mockImplementation((url, options) => ({
  url,
  method: options?.method || 'GET',
  headers: new Map(Object.entries(options?.headers || {})),
  json: jest.fn().mockResolvedValue({}),
  text: jest.fn().mockResolvedValue(''),
}))

// Mock NextRequest specifically
jest.mock('next/server', () => {
  const originalModule = jest.requireActual('next/server')

  return {
    ...originalModule,
    NextRequest: jest.fn().mockImplementation((url, options) => ({
      url,
      method: options?.method || 'GET',
      headers: new Map(Object.entries(options?.headers || {})),
      json: jest.fn().mockResolvedValue({}),
      text: jest.fn().mockResolvedValue(''),
      cookies: {
        get: jest.fn(),
        set: jest.fn(),
        delete: jest.fn(),
      },
    })),
    NextResponse: {
      json: jest.fn().mockImplementation((data, options) => {
        const response = {
          status: options?.status || 200,
          statusText: options?.statusText || 'OK',
          headers: new Map(Object.entries(options?.headers || {})),
          json: jest.fn().mockResolvedValue(data),
          text: jest.fn().mockResolvedValue(JSON.stringify(data)),
          cookies: {
            set: jest.fn(),
            get: jest.fn(),
            delete: jest.fn(),
          },
        }
        return response
      }),
    },
  }
})

global.Response = jest.fn().mockImplementation((body, options) => ({
  status: options?.status || 200,
  statusText: options?.statusText || 'OK',
  headers: new Map(Object.entries(options?.headers || {})),
  json: jest.fn().mockResolvedValue(JSON.parse(body || '{}')),
  text: jest.fn().mockResolvedValue(body || ''),
}))

// Add static json method to Response
global.Response.json = jest.fn().mockImplementation((data, options) => {
  const response = {
    status: options?.status || 200,
    statusText: options?.statusText || 'OK',
    headers: new Map(Object.entries(options?.headers || {})),
    json: jest.fn().mockResolvedValue(data),
    text: jest.fn().mockResolvedValue(JSON.stringify(data)),
    cookies: {
      set: jest.fn(),
      get: jest.fn(),
      delete: jest.fn(),
    },
  }
  return response
})



// Setup for each test
beforeEach(() => {
  fetch.mockClear()
})
