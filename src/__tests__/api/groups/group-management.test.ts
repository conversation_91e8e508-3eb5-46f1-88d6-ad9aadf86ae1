import { describe, it, expect, beforeEach, afterEach } from '@jest/globals'
import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { generateToken } from '@/lib/auth'

// Import the API handlers
import { DELETE } from '@/app/api/groups/[groupId]/route'
import { POST as JOIN } from '@/app/api/groups/[groupId]/join/route'
import { POST as LEAVE } from '@/app/api/groups/[groupId]/leave/route'
import { POST as TRANSFER } from '@/app/api/groups/[groupId]/transfer-ownership/route'

// Helper function to create mock request with auth
function createMockRequest(url: string, method: string, token: string, body?: any) {
  const request = new NextRequest(url, {
    method,
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    ...(body && { body: JSON.stringify(body) })
  })

  // Mock cookies.get method
  Object.defineProperty(request, 'cookies', {
    value: {
      get: jest.fn().mockReturnValue({ value: token })
    }
  })

  // Mock json method if body is provided
  if (body) {
    request.json = jest.fn().mockResolvedValue(body)
  }

  return request
}

describe('Group Management API', () => {
  let testUser1: any
  let testUser2: any
  let testUser3: any
  let testGroup: any
  let privateGroup: any
  let authToken1: string
  let authToken2: string
  let authToken3: string

  beforeEach(async () => {
    // Create test users
    testUser1 = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        username: 'testuser1',
        password: 'hashedpassword',
        name: 'Test User 1'
      }
    })

    testUser2 = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        username: 'testuser2',
        password: 'hashedpassword',
        name: 'Test User 2'
      }
    })

    testUser3 = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        username: 'testuser3',
        password: 'hashedpassword',
        name: 'Test User 3'
      }
    })

    // Generate auth tokens
    authToken1 = generateToken({ userId: testUser1.id, email: testUser1.email, username: testUser1.username })
    authToken2 = generateToken({ userId: testUser2.id, email: testUser2.email, username: testUser2.username })
    authToken3 = generateToken({ userId: testUser3.id, email: testUser3.email, username: testUser3.username })

    // Create test groups
    testGroup = await prisma.group.create({
      data: {
        name: 'Test Public Group',
        description: 'A test public group',
        isPrivate: false,
        ownerId: testUser1.id,
        members: {
          create: {
            userId: testUser1.id,
            role: 'OWNER'
          }
        }
      }
    })

    privateGroup = await prisma.group.create({
      data: {
        name: 'Test Private Group',
        description: 'A test private group',
        isPrivate: true,
        ownerId: testUser1.id,
        members: {
          create: {
            userId: testUser1.id,
            role: 'OWNER'
          }
        }
      }
    })
  })

  afterEach(async () => {
    // Clean up test data
    await prisma.groupMember.deleteMany({})
    await prisma.group.deleteMany({})
    await prisma.user.deleteMany({})
  })

  describe('Join Group', () => {
    it('should allow joining public groups', async () => {
      const request = createMockRequest('http://localhost/api/groups/test/join', 'POST', authToken2)
      const params = Promise.resolve({ groupId: testGroup.id })
      const response = await JOIN(request, { params })
      const data = await response.json()

      console.log('Response status:', response.status)
      console.log('Response data:', data)

      expect(response.status).toBe(200)
      expect(data.message).toBe('Successfully joined the group')
      expect(data.member.role).toBe('MEMBER')
    })

    it('should reject joining private groups', async () => {
      const request = createMockRequest('http://localhost/api/groups/test/join', 'POST', authToken2)
      const params = Promise.resolve({ groupId: privateGroup.id })
      const response = await JOIN(request, { params })
      const data = await response.json()

      expect(response.status).toBe(403)
      expect(data.error).toContain('private group')
    })

    it('should reject joining if already a member', async () => {
      // First join
      await prisma.groupMember.create({
        data: {
          userId: testUser2.id,
          groupId: testGroup.id,
          role: 'MEMBER'
        }
      })

      const request = createMockRequest('http://localhost/api/groups/test/join', 'POST', authToken2)
      const params = Promise.resolve({ groupId: testGroup.id })
      const response = await JOIN(request, { params })
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toContain('already a member')
    })
  })

  describe('Leave Group', () => {
    beforeEach(async () => {
      // Add testUser2 as member
      await prisma.groupMember.create({
        data: {
          userId: testUser2.id,
          groupId: testGroup.id,
          role: 'MEMBER'
        }
      })
    })

    it('should allow members to leave group', async () => {
      const request = createMockRequest('http://localhost/api/groups/test/leave', 'POST', authToken2)
      const params = Promise.resolve({ groupId: testGroup.id })
      const response = await LEAVE(request, { params })
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.message).toBe('Successfully left the group')
    })

    it('should prevent owner from leaving with other members', async () => {
      const request = createMockRequest('http://localhost/api/groups/test/leave', 'POST', authToken1)
      const params = Promise.resolve({ groupId: testGroup.id })
      const response = await LEAVE(request, { params })
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toContain('cannot leave while there are other members')
    })
  })

  describe('Transfer Ownership', () => {
    beforeEach(async () => {
      // Add testUser2 as admin
      await prisma.groupMember.create({
        data: {
          userId: testUser2.id,
          groupId: testGroup.id,
          role: 'ADMIN'
        }
      })
    })

    it('should allow owner to transfer ownership to admin', async () => {
      const request = createMockRequest(
        'http://localhost/api/groups/test/transfer-ownership',
        'POST',
        authToken1,
        { newOwnerId: testUser2.id }
      )

      const params = Promise.resolve({ groupId: testGroup.id })
      const response = await TRANSFER(request, { params })
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.message).toBe('Ownership transferred successfully')
      expect(data.transfer.newOwner.id).toBe(testUser2.id)
    })

    it('should reject transfer to non-member', async () => {
      const request = createMockRequest(
        'http://localhost/api/groups/test/transfer-ownership',
        'POST',
        authToken1,
        { newOwnerId: testUser3.id }
      )

      const params = Promise.resolve({ groupId: testGroup.id })
      const response = await TRANSFER(request, { params })
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toContain('must be a member')
    })
  })

  describe('Delete Group', () => {
    it('should allow owner to delete group', async () => {
      const request = createMockRequest('http://localhost/api/groups/test', 'DELETE', authToken1)
      const params = Promise.resolve({ groupId: testGroup.id })
      const response = await DELETE(request, { params })
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.message).toBe('Group deleted successfully')
    })

    it('should reject deletion by non-owner', async () => {
      const request = createMockRequest('http://localhost/api/groups/test', 'DELETE', authToken2)
      const params = Promise.resolve({ groupId: testGroup.id })
      const response = await DELETE(request, { params })
      const data = await response.json()

      expect(response.status).toBe(404)
      expect(data.error).toContain('insufficient permissions')
    })
  })
})
