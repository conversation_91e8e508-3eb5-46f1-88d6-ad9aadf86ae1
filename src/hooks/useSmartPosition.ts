'use client'

import { useEffect, useState, RefObject } from 'react'

interface Position {
  top?: number
  bottom?: number
  left?: number
  right?: number
  transform?: string
}

interface UseSmartPositionOptions {
  preferredPosition?: 'bottom' | 'top' | 'left' | 'right'
  offset?: number
  menuHeight?: number
  menuWidth?: number
}

export function useSmartPosition(
  triggerRef: RefObject<HTMLElement | null>,
  isOpen: boolean,
  options: UseSmartPositionOptions = {}
) {
  const {
    preferredPosition = 'bottom',
    offset = 8,
    menuHeight = 300, // Estimated menu height
    menuWidth = 224, // 56 * 4 (w-56)
  } = options

  const [position, setPosition] = useState<Position>({})
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    if (!isOpen || !triggerRef.current) {
      setIsVisible(false)
      return
    }

    const calculatePosition = () => {
      const trigger = triggerRef.current
      if (!trigger) return

      const triggerRect = trigger.getBoundingClientRect()
      const viewportHeight = window.innerHeight
      const viewportWidth = window.innerWidth
      const scrollY = window.scrollY
      const scrollX = window.scrollX

      const newPosition: Position = {}

      // Calculate available space in each direction
      const spaceBelow = viewportHeight - triggerRect.bottom
      const spaceAbove = triggerRect.top
      const spaceRight = viewportWidth - triggerRect.left
      const spaceLeft = triggerRect.right

      // Determine vertical position
      if (preferredPosition === 'bottom' && spaceBelow >= menuHeight + offset) {
        // Position below (preferred)
        newPosition.top = triggerRect.bottom + scrollY + offset
      } else if (spaceAbove >= menuHeight + offset) {
        // Position above
        newPosition.bottom = viewportHeight - triggerRect.top - scrollY + offset
      } else if (spaceBelow >= spaceAbove) {
        // Position below with viewport constraint
        newPosition.top = triggerRect.bottom + scrollY + offset
      } else {
        // Position above with viewport constraint
        newPosition.bottom = viewportHeight - triggerRect.top - scrollY + offset
      }

      // Determine horizontal position
      if (spaceRight >= menuWidth) {
        // Position to the right (default)
        newPosition.left = triggerRect.left + scrollX
      } else if (spaceLeft >= menuWidth) {
        // Position to the left
        newPosition.right = viewportWidth - triggerRect.right - scrollX
      } else {
        // Center horizontally if neither side has enough space
        const centerX = triggerRect.left + triggerRect.width / 2
        newPosition.left = Math.max(8, Math.min(centerX - menuWidth / 2, viewportWidth - menuWidth - 8))
        newPosition.transform = 'translateX(0)'
      }

      setPosition(newPosition)
      setIsVisible(true)
    }

    // Calculate position immediately
    calculatePosition()

    // Recalculate on scroll and resize
    const handleResize = () => calculatePosition()
    const handleScroll = () => calculatePosition()

    window.addEventListener('resize', handleResize)
    window.addEventListener('scroll', handleScroll, true)

    return () => {
      window.removeEventListener('resize', handleResize)
      window.removeEventListener('scroll', handleScroll, true)
    }
  }, [isOpen, triggerRef, preferredPosition, offset, menuHeight, menuWidth])

  const getPositionClasses = () => {
    if (!isVisible) return 'opacity-0 invisible'

    let classes = 'opacity-100 visible transition-all duration-200 '

    if (position.top !== undefined) {
      classes += 'top-auto '
    }
    if (position.bottom !== undefined) {
      classes += 'bottom-auto '
    }
    if (position.left !== undefined) {
      classes += 'left-auto '
    }
    if (position.right !== undefined) {
      classes += 'right-auto '
    }

    return classes
  }

  const getPositionStyles = (): React.CSSProperties => {
    if (!isVisible) return { opacity: 0, visibility: 'hidden' }

    return {
      position: 'fixed',
      top: position.top,
      bottom: position.bottom,
      left: position.left,
      right: position.right,
      transform: position.transform,
      opacity: 1,
      visibility: 'visible',
      zIndex: 50,
    }
  }

  return {
    position,
    isVisible,
    getPositionClasses,
    getPositionStyles,
  }
}
