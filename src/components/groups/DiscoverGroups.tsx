'use client'

import { useState, useEffect } from 'react'
import { useGroups } from '@/contexts/GroupContext'

interface PublicGroup {
  id: string
  name: string
  description?: string
  isPrivate: boolean
  memberCount: number
  messageCount: number
  noteCount: number
  owner: {
    id: string
    username: string
  }
  canJoin: boolean
}

interface DiscoverGroupsProps {
  isOpen: boolean
  onClose: () => void
}

export default function DiscoverGroups({ isOpen, onClose }: DiscoverGroupsProps) {
  const { joinGroup, refreshGroups } = useGroups()
  const [groups, setGroups] = useState<PublicGroup[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [search, setSearch] = useState('')
  const [page, setPage] = useState(1)
  const [hasMore, setHasMore] = useState(true)
  const [joiningGroupId, setJoiningGroupId] = useState<string | null>(null)

  const fetchGroups = async (searchTerm = '', pageNum = 1, reset = false) => {
    setLoading(true)
    setError('')

    try {
      const params = new URLSearchParams({
        page: pageNum.toString(),
        limit: '10',
        ...(searchTerm && { search: searchTerm })
      })

      const response = await fetch(`/api/groups/discover?${params}`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch groups')
      }

      const data = await response.json()
      
      if (reset) {
        setGroups(data.groups)
      } else {
        setGroups(prev => [...prev, ...data.groups])
      }
      
      setHasMore(data.pagination.hasNextPage)
      setPage(pageNum)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load groups')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (isOpen) {
      fetchGroups('', 1, true)
    }
  }, [isOpen])

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    fetchGroups(search, 1, true)
  }

  const handleJoinGroup = async (groupId: string) => {
    setJoiningGroupId(groupId)
    setError('')

    try {
      await joinGroup(groupId)
      await refreshGroups()
      // Remove the joined group from the list
      setGroups(prev => prev.filter(g => g.id !== groupId))
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to join group')
    } finally {
      setJoiningGroupId(null)
    }
  }

  const loadMore = () => {
    if (!loading && hasMore) {
      fetchGroups(search, page + 1, false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-full max-w-4xl mx-4 h-[80vh] flex flex-col">
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-2xl font-bold text-gray-900">Discover Public Groups</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Search */}
          <form onSubmit={handleSearch} className="flex space-x-2">
            <input
              type="text"
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              placeholder="Search groups..."
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 high-contrast-input"
            />
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50"
            >
              Search
            </button>
          </form>
        </div>

        {/* Error */}
        {error && (
          <div className="mx-6 mt-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            {error}
          </div>
        )}

        {/* Groups List */}
        <div className="flex-1 overflow-y-auto p-6">
          {loading && groups.length === 0 ? (
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="animate-pulse bg-gray-200 h-24 rounded-lg"></div>
              ))}
            </div>
          ) : groups.length === 0 ? (
            <div className="text-center py-12">
              <svg className="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No public groups found</h3>
              <p className="text-gray-500">Try adjusting your search terms or check back later.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {groups.map((group) => (
                <div key={group.id} className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="flex items-center mb-2">
                        <h3 className="text-lg font-medium text-gray-900">{group.name}</h3>
                        <span className="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          Public
                        </span>
                      </div>
                      
                      {group.description && (
                        <p className="text-gray-600 mb-3">{group.description}</p>
                      )}
                      
                      <div className="flex items-center text-sm text-gray-500 space-x-4">
                        <span>{group.memberCount} members</span>
                        <span>{group.messageCount} messages</span>
                        <span>{group.noteCount} notes</span>
                        <span>by @{group.owner.username}</span>
                      </div>
                    </div>
                    
                    <div className="ml-4">
                      {group.canJoin ? (
                        <button
                          onClick={() => handleJoinGroup(group.id)}
                          disabled={joiningGroupId === group.id}
                          className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50"
                        >
                          {joiningGroupId === group.id ? 'Joining...' : 'Join'}
                        </button>
                      ) : (
                        <span className="px-4 py-2 bg-gray-100 text-gray-500 rounded-md">
                          Already joined
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              ))}
              
              {hasMore && (
                <div className="text-center pt-4">
                  <button
                    onClick={loadMore}
                    disabled={loading}
                    className="px-6 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 disabled:opacity-50"
                  >
                    {loading ? 'Loading...' : 'Load More'}
                  </button>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
