import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getAuthenticatedUser } from '@/lib/middleware'

// GET /api/groups/[groupId]/messages/[messageId]/thread - Get thread for a specific message
export async function GET(
  request: NextRequest, 
  { params }: { params: Promise<{ groupId: string; messageId: string }> }
) {
  try {
    const user = await getAuthenticatedUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    const { groupId, messageId } = await params

    // Check if user is member of the group
    const groupMember = await prisma.groupMember.findFirst({
      where: {
        groupId: groupId,
        userId: user.id
      }
    })

    if (!groupMember) {
      return NextResponse.json(
        { error: 'Access denied. You are not a member of this group.' },
        { status: 403 }
      )
    }

    // Get the root message of the thread
    const rootMessage = await prisma.message.findFirst({
      where: {
        id: messageId,
        groupId: groupId
      },
      include: {
        author: {
          select: {
            id: true,
            username: true,
            name: true,
            avatar: true,
          }
        }
      }
    })

    if (!rootMessage) {
      return NextResponse.json(
        { error: 'Message not found' },
        { status: 404 }
      )
    }

    // If this message is a reply, get the actual root message
    let actualRootId = messageId
    if (rootMessage.parentMessageId) {
      // Find the root message by traversing up the thread
      let currentMessage = rootMessage
      while (currentMessage.parentMessageId) {
        const parentMessage = await prisma.message.findUnique({
          where: { id: currentMessage.parentMessageId },
          include: {
            author: {
              select: {
                id: true,
                username: true,
                name: true,
                avatar: true,
              }
            }
          }
        })
        if (!parentMessage) break
        currentMessage = parentMessage
      }
      actualRootId = currentMessage.id
    }

    // Get the complete thread starting from the root
    const threadMessages = await prisma.message.findMany({
      where: {
        OR: [
          { id: actualRootId },
          { parentMessageId: actualRootId },
          // Get nested replies (up to 3 levels deep)
          {
            parentMessage: {
              parentMessageId: actualRootId
            }
          },
          {
            parentMessage: {
              parentMessage: {
                parentMessageId: actualRootId
              }
            }
          }
        ],
        groupId: groupId
      },
      include: {
        author: {
          select: {
            id: true,
            username: true,
            name: true,
            avatar: true,
          }
        },
        parentMessage: {
          select: {
            id: true,
            content: true,
            author: {
              select: {
                id: true,
                username: true,
                name: true,
              }
            }
          }
        },
        _count: {
          select: {
            replies: true
          }
        }
      },
      orderBy: {
        createdAt: 'asc'
      }
    })

    // Organize messages into a hierarchical structure
    const messageMap = new Map()
    const rootMessages: any[] = []

    // First pass: create map of all messages
    threadMessages.forEach(message => {
      messageMap.set(message.id, { ...message, replies: [] })
    })

    // Second pass: organize into hierarchy
    threadMessages.forEach(message => {
      if (message.parentMessageId) {
        const parent = messageMap.get(message.parentMessageId)
        if (parent) {
          parent.replies.push(messageMap.get(message.id))
        }
      } else {
        rootMessages.push(messageMap.get(message.id))
      }
    })

    return NextResponse.json({
      thread: rootMessages[0] || null,
      totalMessages: threadMessages.length
    })
  } catch (error) {
    console.error('Get thread error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch thread' },
      { status: 500 }
    )
  }
}
