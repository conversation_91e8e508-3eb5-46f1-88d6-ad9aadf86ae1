#!/bin/bash

# User Role Management Testing Script
# Script untuk testing fungsi promote/demote/kick user
# Usage: ./scripts/test-user-role-management.sh

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
API_BASE="https://mybinder.vercel.app/api"
COOKIES_DIR="./test_cookies"

# Create cookies directory
mkdir -p $COOKIES_DIR

echo -e "${BLUE}🧪 User Role Management Testing${NC}"
echo -e "${BLUE}===============================${NC}"
echo ""

# Function to test endpoint
test_endpoint() {
    local method=$1
    local endpoint=$2
    local cookies_file=$3
    local data=$4
    local expected_status=$5
    local description=$6
    
    echo -n "Testing $description... "
    
    if [ -n "$data" ]; then
        response=$(curl -s -w "%{http_code}" -X $method "$API_BASE$endpoint" \
            -H "Content-Type: application/json" \
            -b "$cookies_file" \
            -d "$data")
    else
        response=$(curl -s -w "%{http_code}" -X $method "$API_BASE$endpoint" \
            -b "$cookies_file")
    fi
    
    status_code="${response: -3}"
    response_body="${response%???}"
    
    if [ "$status_code" = "$expected_status" ]; then
        echo -e "${GREEN}✅ PASS${NC} ($status_code)"
        if [ "$status_code" != "200" ] && [ "$status_code" != "201" ]; then
            echo "Response: $response_body"
        fi
    else
        echo -e "${RED}❌ FAIL${NC} (Expected: $expected_status, Got: $status_code)"
        echo "Response: $response_body"
    fi
}

# Function to login and save cookies
login_user() {
    local email=$1
    local password=$2
    local cookies_file=$3
    local description=$4
    
    echo -n "Logging in $description... "
    
    response=$(curl -s -w "%{http_code}" -X POST "$API_BASE/auth/login" \
        -H "Content-Type: application/json" \
        -c "$cookies_file" \
        -d "{\"email\": \"$email\", \"password\": \"$password\"}")
    
    status_code="${response: -3}"
    
    if [ "$status_code" = "200" ]; then
        echo -e "${GREEN}✅ SUCCESS${NC}"
        return 0
    else
        echo -e "${RED}❌ FAILED${NC}"
        return 1
    fi
}

echo -e "${YELLOW}📝 Step 1: Login dengan berbagai akun demo${NC}"
echo ""

# Login with different demo accounts
login_user "<EMAIL>" "demo123" "$COOKIES_DIR/owner1_cookies.txt" "Owner 1"
login_user "<EMAIL>" "demo123" "$COOKIES_DIR/admin1_cookies.txt" "Admin 1"
login_user "<EMAIL>" "demo123" "$COOKIES_DIR/member1_cookies.txt" "Member 1"
login_user "<EMAIL>" "demo123" "$COOKIES_DIR/test_cookies.txt" "Test User"

echo ""
echo -e "${YELLOW}🏢 Step 2: Get Group ID for Testing${NC}"
echo ""

# Get a group ID first
echo -n "Getting group ID for testing... "
groups_response=$(curl -s "$API_BASE/groups" -b "$COOKIES_DIR/owner1_cookies.txt")
group_id=$(echo "$groups_response" | grep -o '"id":"[^"]*"' | head -1 | cut -d'"' -f4)

if [ -n "$group_id" ]; then
    echo -e "${GREEN}✅ Found group ID: $group_id${NC}"
else
    echo -e "${RED}❌ Could not get group ID${NC}"
    exit 1
fi

echo ""
echo -e "${YELLOW}👥 Step 3: Add Members for Testing${NC}"
echo ""

# Add test user as member
echo -n "Adding test user as member... "
add_member_response=$(curl -s -w "%{http_code}" -X POST "$API_BASE/groups/$group_id/members" \
    -H "Content-Type: application/json" \
    -b "$COOKIES_DIR/owner1_cookies.txt" \
    -d '{"email":"<EMAIL>","role":"MEMBER"}')

add_status="${add_member_response: -3}"
if [ "$add_status" = "200" ]; then
    echo -e "${GREEN}✅ SUCCESS${NC}"
else
    echo -e "${RED}❌ FAILED${NC} ($add_status)"
    echo "Response: ${add_member_response%???}"
fi

# Add admin1 as admin
echo -n "Adding admin1 as admin... "
add_admin_response=$(curl -s -w "%{http_code}" -X POST "$API_BASE/groups/$group_id/members" \
    -H "Content-Type: application/json" \
    -b "$COOKIES_DIR/owner1_cookies.txt" \
    -d '{"email":"<EMAIL>","role":"ADMIN"}')

add_admin_status="${add_admin_response: -3}"
if [ "$add_admin_status" = "200" ]; then
    echo -e "${GREEN}✅ SUCCESS${NC}"
else
    echo -e "${RED}❌ FAILED${NC} ($add_admin_status)"
    echo "Response: ${add_admin_response%???}"
fi

echo ""
echo -e "${YELLOW}👥 Step 4: Get Member IDs for Testing${NC}"
echo ""

# Get group details to find member IDs
echo -n "Getting group members... "
group_response=$(curl -s "$API_BASE/groups/$group_id" -b "$COOKIES_DIR/owner1_cookies.txt")

# Extract member IDs using a more robust approach
# First, let's see the full response for debugging
echo "Group response for debugging:"
echo "$group_response" | jq '.' 2>/dev/null || echo "$group_response"

# Extract member IDs by finding the member objects
test_member_id=$(echo "$group_response" | jq -r '.group.members[] | select(.user.username == "testuser") | .id' 2>/dev/null)
admin_member_id=$(echo "$group_response" | jq -r '.group.members[] | select(.user.username == "admin1") | .id' 2>/dev/null)

# Fallback to grep if jq is not available
if [ -z "$test_member_id" ] || [ "$test_member_id" = "null" ]; then
    test_member_id=$(echo "$group_response" | grep -B 5 -A 5 '"username":"testuser"' | grep '"id":"' | head -1 | sed 's/.*"id":"\([^"]*\)".*/\1/')
fi

if [ -z "$admin_member_id" ] || [ "$admin_member_id" = "null" ]; then
    admin_member_id=$(echo "$group_response" | grep -B 5 -A 5 '"username":"admin1"' | grep '"id":"' | head -1 | sed 's/.*"id":"\([^"]*\)".*/\1/')
fi

if [ -n "$test_member_id" ] && [ -n "$admin_member_id" ]; then
    echo -e "${GREEN}✅ Found member IDs${NC}"
    echo "Test member ID: $test_member_id"
    echo "Admin member ID: $admin_member_id"
else
    echo -e "${RED}❌ Could not get member IDs${NC}"
    echo "Test member ID: $test_member_id"
    echo "Admin member ID: $admin_member_id"
    echo "Group response: $group_response"
fi

echo ""
echo -e "${YELLOW}🔄 Step 5: Testing Role Management${NC}"
echo ""

if [ -n "$test_member_id" ]; then
    # Test promote member to admin (by owner)
    test_endpoint "PUT" "/groups/$group_id/members/$test_member_id/role" "$COOKIES_DIR/owner1_cookies.txt" '{"role":"ADMIN"}' "200" "Promote member to admin (by owner)"

    # Test demote admin to member (by owner)
    test_endpoint "PUT" "/groups/$group_id/members/$test_member_id/role" "$COOKIES_DIR/owner1_cookies.txt" '{"role":"MEMBER"}' "200" "Demote admin to member (by owner)"
fi

if [ -n "$admin_member_id" ] && [ -n "$test_member_id" ]; then
    # Test promote member to admin (by admin) - should fail
    test_endpoint "PUT" "/groups/$group_id/members/$test_member_id/role" "$COOKIES_DIR/admin1_cookies.txt" '{"role":"ADMIN"}' "403" "Promote member to admin (by admin - should fail)"

    # Test demote admin (by admin) - should fail
    test_endpoint "PUT" "/groups/$group_id/members/$admin_member_id/role" "$COOKIES_DIR/admin1_cookies.txt" '{"role":"MEMBER"}' "403" "Demote admin (by admin - should fail)"
fi

echo ""
echo -e "${YELLOW}🚫 Step 6: Testing Member Removal${NC}"
echo ""

if [ -n "$test_member_id" ]; then
    # Test remove member (by owner)
    test_endpoint "DELETE" "/groups/$group_id/members?memberId=$test_member_id" "$COOKIES_DIR/owner1_cookies.txt" "" "200" "Remove member (by owner)"
fi

if [ -n "$admin_member_id" ]; then
    # Test remove admin (by admin) - should fail
    test_endpoint "DELETE" "/groups/$group_id/members?memberId=$admin_member_id" "$COOKIES_DIR/admin1_cookies.txt" "" "403" "Remove admin (by admin - should fail)"
fi

echo ""
echo -e "${YELLOW}🚫 Step 7: Testing Error Cases${NC}"
echo ""

# Test invalid role
if [ -n "$test_member_id" ]; then
    test_endpoint "PUT" "/groups/$group_id/members/$test_member_id/role" "$COOKIES_DIR/owner1_cookies.txt" '{"role":"INVALID"}' "400" "Invalid role"
fi

# Test non-existent member
test_endpoint "PUT" "/groups/$group_id/members/invalid-member-id/role" "$COOKIES_DIR/owner1_cookies.txt" '{"role":"ADMIN"}' "404" "Non-existent member"

# Test unauthorized access
if [ -n "$test_member_id" ]; then
    test_endpoint "PUT" "/groups/$group_id/members/$test_member_id/role" "$COOKIES_DIR/member1_cookies.txt" '{"role":"ADMIN"}' "403" "Unauthorized role change"
fi

echo ""
echo -e "${BLUE}📊 Testing Summary${NC}"
echo -e "${BLUE}=================${NC}"
echo ""
echo -e "${GREEN}✅ User role management endpoints tested${NC}"
echo -e "${GREEN}✅ Permission system tested${NC}"
echo -e "${GREEN}✅ Error handling tested${NC}"
echo ""
echo -e "${YELLOW}🧹 Cleaning up test cookies...${NC}"
rm -rf $COOKIES_DIR
echo -e "${GREEN}✅ Cleanup complete${NC}"
echo ""
echo -e "${BLUE}🎉 User role management testing completed!${NC}"
