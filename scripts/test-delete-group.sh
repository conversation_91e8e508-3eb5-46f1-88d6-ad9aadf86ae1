#!/bin/bash

# Delete Group Testing Script
# Script untuk testing fungsi delete group dan memverifikasi apakah grup benar-benar terhapus
# Usage: ./scripts/test-delete-group.sh

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
API_BASE="https://mybinder.vercel.app/api"
COOKIES_DIR="./test_cookies"

# Create cookies directory
mkdir -p $COOKIES_DIR

echo -e "${BLUE}🧪 Delete Group Testing${NC}"
echo -e "${BLUE}=====================${NC}"
echo ""

# Function to test endpoint
test_endpoint() {
    local method=$1
    local endpoint=$2
    local cookies_file=$3
    local data=$4
    local expected_status=$5
    local description=$6
    
    echo -n "Testing $description... "
    
    if [ -n "$data" ]; then
        response=$(curl -s -w "%{http_code}" -X $method "$API_BASE$endpoint" \
            -H "Content-Type: application/json" \
            -b "$cookies_file" \
            -d "$data")
    else
        response=$(curl -s -w "%{http_code}" -X $method "$API_BASE$endpoint" \
            -b "$cookies_file")
    fi
    
    status_code="${response: -3}"
    response_body="${response%???}"
    
    if [ "$status_code" = "$expected_status" ]; then
        echo -e "${GREEN}✅ PASS${NC} ($status_code)"
        if [ "$expected_status" = "200" ]; then
            echo "Response: $response_body"
        fi
    else
        echo -e "${RED}❌ FAIL${NC} (Expected: $expected_status, Got: $status_code)"
        echo "Response: $response_body"
    fi
    
    # Return the response body for further processing
    echo "$response_body"
}

# Function to login and save cookies
login_user() {
    local email=$1
    local password=$2
    local cookies_file=$3
    local description=$4
    
    echo -n "Logging in $description... "
    
    response=$(curl -s -w "%{http_code}" -X POST "$API_BASE/auth/login" \
        -H "Content-Type: application/json" \
        -c "$cookies_file" \
        -d "{\"email\": \"$email\", \"password\": \"$password\"}")
    
    status_code="${response: -3}"
    
    if [ "$status_code" = "200" ]; then
        echo -e "${GREEN}✅ SUCCESS${NC}"
        return 0
    else
        echo -e "${RED}❌ FAILED${NC}"
        return 1
    fi
}

echo -e "${YELLOW}📝 Step 1: Login sebagai owner${NC}"
echo ""

# Login as owner
login_user "<EMAIL>" "demo123" "$COOKIES_DIR/owner1_cookies.txt" "Owner 1"

echo ""
echo -e "${YELLOW}🏢 Step 2: Create Test Group${NC}"
echo ""

# Create a test group
echo -n "Creating test group... "
create_response=$(curl -s -w "%{http_code}" -X POST "$API_BASE/groups" \
    -H "Content-Type: application/json" \
    -b "$COOKIES_DIR/owner1_cookies.txt" \
    -d '{"name":"Delete Test Group","description":"Group for testing delete functionality","isPrivate":false}')

create_status="${create_response: -3}"
create_body="${create_response%???}"

if [ "$create_status" = "200" ]; then
    echo -e "${GREEN}✅ SUCCESS${NC}"
    # Extract group ID
    test_group_id=$(echo "$create_body" | jq -r '.group.id' 2>/dev/null)
    if [ -z "$test_group_id" ] || [ "$test_group_id" = "null" ]; then
        test_group_id=$(echo "$create_body" | grep -o '"id":"[^"]*"' | head -1 | cut -d'"' -f4)
    fi
    echo "Created group ID: $test_group_id"
else
    echo -e "${RED}❌ FAILED${NC} ($create_status)"
    echo "Response: $create_body"
    exit 1
fi

echo ""
echo -e "${YELLOW}💬 Step 3: Add Content to Group${NC}"
echo ""

# Add a message to the group
echo -n "Adding message to group... "
message_response=$(curl -s -w "%{http_code}" -X POST "$API_BASE/groups/$test_group_id/messages" \
    -H "Content-Type: application/json" \
    -b "$COOKIES_DIR/owner1_cookies.txt" \
    -d '{"content":"Test message for delete testing"}')

message_status="${message_response: -3}"
if [ "$message_status" = "200" ]; then
    echo -e "${GREEN}✅ SUCCESS${NC}"
else
    echo -e "${RED}❌ FAILED${NC} ($message_status)"
    echo "Response: ${message_response%???}"
fi

# Add a note to the group
echo -n "Adding note to group... "
note_response=$(curl -s -w "%{http_code}" -X POST "$API_BASE/groups/$test_group_id/notes" \
    -H "Content-Type: application/json" \
    -b "$COOKIES_DIR/owner1_cookies.txt" \
    -d '{"title":"Test Note","description":"Note for delete testing"}')

note_status="${note_response: -3}"
if [ "$note_status" = "200" ]; then
    echo -e "${GREEN}✅ SUCCESS${NC}"
else
    echo -e "${RED}❌ FAILED${NC} ($note_status)"
    echo "Response: ${note_response%???}"
fi

echo ""
echo -e "${YELLOW}🔍 Step 4: Verify Group Exists Before Delete${NC}"
echo ""

# Verify group exists
echo -n "Verifying group exists... "
verify_response=$(curl -s -w "%{http_code}" -X GET "$API_BASE/groups/$test_group_id" \
    -b "$COOKIES_DIR/owner1_cookies.txt")

verify_status="${verify_response: -3}"
verify_body="${verify_response%???}"

if [ "$verify_status" = "200" ]; then
    echo -e "${GREEN}✅ GROUP EXISTS${NC}"
    echo "Group details: $verify_body"
else
    echo -e "${RED}❌ GROUP NOT FOUND${NC} ($verify_status)"
    echo "Response: $verify_body"
fi

echo ""
echo -e "${YELLOW}🗑️ Step 5: Delete Group via API${NC}"
echo ""

# Delete the group
echo -n "Deleting group via API... "
delete_response=$(curl -s -w "%{http_code}" -X DELETE "$API_BASE/groups/$test_group_id" \
    -b "$COOKIES_DIR/owner1_cookies.txt")

delete_status="${delete_response: -3}"
delete_body="${delete_response%???}"

if [ "$delete_status" = "200" ]; then
    echo -e "${GREEN}✅ DELETE API CALL SUCCESS${NC}"
    echo "Delete response: $delete_body"
else
    echo -e "${RED}❌ DELETE API CALL FAILED${NC} ($delete_status)"
    echo "Response: $delete_body"
fi

echo ""
echo -e "${YELLOW}🔍 Step 6: Verify Group is Actually Deleted${NC}"
echo ""

# Wait a moment for deletion to complete
sleep 2

# Try to access the deleted group
echo -n "Verifying group is deleted... "
verify_deleted_response=$(curl -s -w "%{http_code}" -X GET "$API_BASE/groups/$test_group_id" \
    -b "$COOKIES_DIR/owner1_cookies.txt")

verify_deleted_status="${verify_deleted_response: -3}"
verify_deleted_body="${verify_deleted_response%???}"

if [ "$verify_deleted_status" = "404" ]; then
    echo -e "${GREEN}✅ GROUP SUCCESSFULLY DELETED${NC}"
    echo "Response: $verify_deleted_body"
else
    echo -e "${RED}❌ GROUP STILL EXISTS${NC} ($verify_deleted_status)"
    echo "Response: $verify_deleted_body"
fi

# Check if group appears in groups list
echo -n "Checking groups list... "
groups_list_response=$(curl -s -w "%{http_code}" -X GET "$API_BASE/groups" \
    -b "$COOKIES_DIR/owner1_cookies.txt")

groups_list_status="${groups_list_response: -3}"
groups_list_body="${groups_list_response%???}"

if [ "$groups_list_status" = "200" ]; then
    # Check if the deleted group ID appears in the list
    if echo "$groups_list_body" | grep -q "$test_group_id"; then
        echo -e "${RED}❌ GROUP STILL IN LIST${NC}"
        echo "Groups list contains deleted group: $test_group_id"
    else
        echo -e "${GREEN}✅ GROUP NOT IN LIST${NC}"
    fi
else
    echo -e "${RED}❌ FAILED TO GET GROUPS LIST${NC} ($groups_list_status)"
    echo "Response: $groups_list_body"
fi

echo ""
echo -e "${BLUE}📊 Testing Summary${NC}"
echo -e "${BLUE}=================${NC}"
echo ""
echo -e "${GREEN}✅ Delete group API endpoint tested${NC}"
echo -e "${GREEN}✅ Group deletion verification completed${NC}"
echo ""
echo -e "${YELLOW}🧹 Cleaning up test cookies...${NC}"
rm -rf $COOKIES_DIR
echo -e "${GREEN}✅ Cleanup complete${NC}"
echo ""
echo -e "${BLUE}🎉 Delete group testing completed!${NC}"
