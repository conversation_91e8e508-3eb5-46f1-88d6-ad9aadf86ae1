# Manual Test untuk Member Management UI

## <PERSON><PERSON><PERSON> yang Dilakukan
- Mengubah dropdown menu menjadi button terpisah untuk manage member
- Menghapus three-dots menu dan dropdown
- Menambahkan button langsung: Promote, Demote, Remove

## Test Cases

### 1. Test sebagai Owner
1. Login sebagai owner grup
2. <PERSON><PERSON><PERSON> ke grup yang Anda miliki
3. Klik tab "Members"
4. Verifikasi:
   - Untuk member biasa: ada button "Promote" dan "Remove"
   - Untuk admin: ada button "Demote" dan "Remove"
   - Untuk owner sendiri: tidak ada button management

### 2. Test Promote Member
1. Klik button "Promote" pada member biasa
2. Veri<PERSON><PERSON>i:
   - Role berubah dari MEMBER ke ADMIN
   - Button berubah dari "Promote" ke "Demote"
   - Tidak ada error di console

### 3. Test Demote Admin
1. Klik button "Demote" pada admin
2. Verifi<PERSON><PERSON>:
   - Role berubah dari ADMIN ke MEMBER
   - Button berubah dari "Demote" ke "Promote"
   - Tidak ada error di console

### 4. Test Remove Member
1. Klik button "Remove" pada member/admin
2. V<PERSON><PERSON><PERSON><PERSON>:
   - Muncul konfirmasi dialog
   - Jika OK: member dihapus dari list
   - Jika Cancel: member tetap ada

### 5. Test sebagai Admin
1. Login sebagai admin grup
2. Masuk ke grup
3. Klik tab "Members"
4. Verifikasi:
   - Admin bisa promote/demote member biasa
   - Admin bisa remove member biasa
   - Admin tidak bisa manage owner
   - Admin tidak bisa manage admin lain

### 6. Test sebagai Member Biasa
1. Login sebagai member biasa
2. Masuk ke grup
3. Klik tab "Members"
4. Verifikasi:
   - Tidak ada button management sama sekali
   - Hanya bisa melihat list member

## Styling yang Diharapkan
- Promote button: bg-blue-100 text-blue-700 hover:bg-blue-200
- Demote button: bg-orange-100 text-orange-700 hover:bg-orange-200
- Remove button: bg-red-100 text-red-700 hover:bg-red-200
- Semua button: px-2 py-1 text-xs rounded transition-colors
- Tooltip pada hover menunjukkan aksi yang akan dilakukan

## Responsive Design
- Button tetap terlihat baik di mobile
- Spacing antar button konsisten
- Text tetap readable di ukuran kecil
