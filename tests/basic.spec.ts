import { test, expect } from '@playwright/test';

test.describe('Basic App Functionality', () => {
  test('should load the homepage', async ({ page }) => {
    await page.goto('/');
    
    // Check if the page loads
    await expect(page).toHaveTitle(/MyBinder/);
  });

  test('should navigate to auth page', async ({ page }) => {
    await page.goto('/auth');

    // Check if auth page loads
    await expect(page.locator('h1')).toContainText('MyBinder');
    await expect(page.locator('h2')).toContainText('Welcome Back');
  });

  test('should show mobile-responsive design', async ({ page, isMobile }) => {
    await page.goto('/');

    if (isMobile) {
      // Check mobile-specific elements - verify page loads and has responsive classes
      await expect(page.locator('body')).toHaveClass(/antialiased/);
      // Check for mobile viewport meta tag
      const viewport = await page.locator('meta[name="viewport"]').getAttribute('content');
      expect(viewport).toContain('width=device-width');
    } else {
      // Check desktop layout - verify page loads properly
      await expect(page.locator('body')).toHaveClass(/antialiased/);
    }
  });
});
