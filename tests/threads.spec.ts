import { test, expect } from '@playwright/test';

test.describe('Thread Functionality', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the app
    await page.goto('/');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
  });

  test('should display reply button on message hover (desktop)', async ({ page, isMobile }) => {
    test.skip(isMobile, 'This test is for desktop only');
    
    // Mock authentication
    await page.route('/api/auth/me', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          id: '1',
          email: '<EMAIL>',
          name: 'Test User'
        })
      });
    });

    // Mock groups
    await page.route('/api/groups', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            id: '1',
            name: 'Test Group',
            description: 'Test Description'
          }
        ])
      });
    });

    // Mock messages
    await page.route('/api/groups/1/messages*', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          messages: [
            {
              id: '1',
              content: 'Test message',
              createdAt: new Date().toISOString(),
              author: {
                id: '2',
                name: 'Other User',
                email: '<EMAIL>'
              },
              parentMessageId: null,
              threadDepth: 0,
              replies: [],
              _count: { replies: 0 }
            }
          ],
          hasMore: false,
          currentPage: 1
        })
      });
    });

    await page.reload();
    
    // Navigate to dashboard
    await page.goto('/dashboard');
    
    // Wait for messages to load
    await page.waitForSelector('[data-testid="message"]', { timeout: 10000 });
    
    // Hover over message
    const message = page.locator('[data-testid="message"]').first();
    await message.hover();
    
    // Check if reply button appears
    const replyButton = page.locator('[data-testid="reply-button"]');
    await expect(replyButton).toBeVisible();
  });

  test('should show mobile thread navigation on mobile', async ({ page, isMobile }) => {
    test.skip(!isMobile, 'This test is for mobile only');
    
    // Mock authentication and data (same as above)
    await page.route('/api/auth/me', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          id: '1',
          email: '<EMAIL>',
          name: 'Test User'
        })
      });
    });

    await page.route('/api/groups', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            id: '1',
            name: 'Test Group',
            description: 'Test Description'
          }
        ])
      });
    });

    await page.route('/api/groups/1/messages*', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          messages: [
            {
              id: '1',
              content: 'Test message with replies',
              createdAt: new Date().toISOString(),
              author: {
                id: '2',
                name: 'Other User',
                email: '<EMAIL>'
              },
              parentMessageId: null,
              threadDepth: 0,
              replies: [],
              _count: { replies: 2 }
            }
          ],
          hasMore: false,
          currentPage: 1
        })
      });
    });

    await page.reload();
    await page.goto('/dashboard');
    
    // Wait for messages to load
    await page.waitForSelector('[data-testid="message"]', { timeout: 10000 });
    
    // Check if mobile thread navigation is visible
    const mobileNav = page.locator('[data-testid="mobile-thread-nav"]');
    await expect(mobileNav).toBeVisible();
  });

  test('should create a reply when reply button is clicked', async ({ page }) => {
    // Mock authentication and initial data
    await page.route('/api/auth/me', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          id: '1',
          email: '<EMAIL>',
          name: 'Test User'
        })
      });
    });

    await page.route('/api/groups', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            id: '1',
            name: 'Test Group',
            description: 'Test Description'
          }
        ])
      });
    });

    await page.route('/api/groups/1/messages*', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          messages: [
            {
              id: '1',
              content: 'Original message',
              createdAt: new Date().toISOString(),
              author: {
                id: '2',
                name: 'Other User',
                email: '<EMAIL>'
              },
              parentMessageId: null,
              threadDepth: 0,
              replies: [],
              _count: { replies: 0 }
            }
          ],
          hasMore: false,
          currentPage: 1
        })
      });
    });

    // Mock reply creation
    await page.route('**/api/groups/1/messages', async route => {
      if (route.request().method() === 'POST') {
        await route.fulfill({
          status: 201,
          contentType: 'application/json',
          body: JSON.stringify({
            id: '2',
            content: 'Reply message',
            createdAt: new Date().toISOString(),
            author: {
              id: '1',
              name: 'Test User',
              email: '<EMAIL>'
            },
            parentMessageId: '1',
            threadDepth: 1
          })
        });
      } else {
        await route.continue();
      }
    });

    await page.reload();
    await page.goto('/dashboard');
    
    // Wait for messages to load
    await page.waitForSelector('[data-testid="message"]', { timeout: 10000 });
    
    // Click reply button
    const message = page.locator('[data-testid="message"]').first();
    await message.hover();
    const replyButton = page.locator('[data-testid="reply-button"]');
    await replyButton.click();
    
    // Check if reply input appears
    const replyInput = page.locator('[data-testid="reply-input"]');
    await expect(replyInput).toBeVisible();
    
    // Type reply and send
    await replyInput.fill('This is a test reply');
    await page.keyboard.press('Enter');
    
    // Verify reply was sent (check for optimistic update)
    await expect(page.locator('text=This is a test reply')).toBeVisible();
  });

  test('should handle swipe gestures on mobile', async ({ page, isMobile }) => {
    test.skip(!isMobile, 'This test is for mobile only');
    
    // Mock data
    await page.route('/api/auth/me', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          id: '1',
          email: '<EMAIL>',
          name: 'Test User'
        })
      });
    });

    await page.route('/api/groups', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            id: '1',
            name: 'Test Group',
            description: 'Test Description'
          }
        ])
      });
    });

    await page.route('/api/groups/1/messages*', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          messages: [
            {
              id: '1',
              content: 'Test message',
              createdAt: new Date().toISOString(),
              author: {
                id: '2',
                name: 'Other User',
                email: '<EMAIL>'
              },
              parentMessageId: null,
              threadDepth: 0,
              replies: [],
              _count: { replies: 0 }
            }
          ],
          hasMore: false,
          currentPage: 1
        })
      });
    });

    await page.reload();
    await page.goto('/dashboard');
    
    // Wait for messages to load
    await page.waitForSelector('[data-testid="message"]', { timeout: 10000 });
    
    // Open reply input
    const replyButton = page.locator('[data-testid="reply-button"]');
    await replyButton.click();
    
    // Verify reply input is open
    const replyInput = page.locator('[data-testid="reply-input"]');
    await expect(replyInput).toBeVisible();
    
    // Simulate swipe left gesture to close reply input
    const messagesContainer = page.locator('[data-testid="messages-container"]');
    await messagesContainer.dispatchEvent('touchstart', {
      touches: [{ clientX: 300, clientY: 200 }]
    });
    await messagesContainer.dispatchEvent('touchmove', {
      touches: [{ clientX: 100, clientY: 200 }]
    });
    await messagesContainer.dispatchEvent('touchend', {
      touches: []
    });
    
    // Verify reply input is closed
    await expect(replyInput).not.toBeVisible();
  });
});
