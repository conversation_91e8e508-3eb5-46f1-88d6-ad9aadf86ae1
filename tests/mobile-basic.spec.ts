import { test, expect } from '@playwright/test'

test.describe('Basic Mobile UI', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('http://localhost:3000')
    
    // Login with demo credentials
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'demo123')
    await page.click('button[type="submit"]')

    // Wait for dashboard to load
    await page.waitForURL('**/dashboard')
    await page.waitForSelector('header', { timeout: 10000 })
  })

  test('should show hamburger menu on mobile', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    
    // Check if hamburger menu is visible
    const hamburgerButton = page.locator('button[aria-label="Toggle sidebar"]')
    await expect(hamburgerButton).toBeVisible()
    
    // Check if MyBinder title is visible
    const title = page.locator('h1:has-text("MyBinder")')
    await expect(title).toBeVisible()
  })

  test('should toggle sidebar on mobile', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    
    // Click hamburger menu
    const hamburgerButton = page.locator('button[aria-label="Toggle sidebar"]')
    await hamburgerButton.click()
    
    // Wait a bit for animation
    await page.waitForTimeout(500)
    
    // Check if Groups header is visible (indicating sidebar is open)
    const groupsHeader = page.locator('h2:has-text("Groups")')
    await expect(groupsHeader).toBeVisible()
  })

  test('should show mobile-optimized layout', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    
    // Check if header is properly sized
    const header = page.locator('header')
    await expect(header).toBeVisible()
    
    // Check if main content area exists
    const mainContent = page.locator('.flex-1').first()
    await expect(mainContent).toBeVisible()
  })

  test('should work on different mobile sizes', async ({ page }) => {
    const viewports = [
      { width: 320, height: 568 }, // Small mobile
      { width: 375, height: 667 }, // Medium mobile
      { width: 414, height: 896 }, // Large mobile
    ]

    for (const viewport of viewports) {
      await page.setViewportSize(viewport)
      
      // Check if hamburger menu is visible
      const hamburgerButton = page.locator('button[aria-label="Toggle sidebar"]')
      await expect(hamburgerButton).toBeVisible()
      
      // Check if header is visible
      const header = page.locator('header')
      await expect(header).toBeVisible()
    }
  })

  test('should show welcome message when no group selected', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })

    // Wait for page to load and ensure no group is selected
    await page.waitForTimeout(1000)

    // Check if welcome message is visible (should show "Welcome to MyBinder" on mobile)
    const welcomeMessage = page.locator('h3:has-text("Welcome to MyBinder")')
    await expect(welcomeMessage).toBeVisible()

    // Check if descriptive text is visible
    const descText = page.locator('text=Tap "Select a Group" to choose a group and start chatting')
    await expect(descText).toBeVisible()
  })
})
