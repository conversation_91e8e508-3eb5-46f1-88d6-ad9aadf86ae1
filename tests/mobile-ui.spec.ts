import { test, expect } from '@playwright/test'

test.describe('Mobile UI Improvements', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('http://localhost:3000')
    
    // Login with demo credentials
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'demo123')
    await page.click('button[type="submit"]')

    // Wait for dashboard to load
    await page.waitForURL('**/dashboard')
    await page.waitForSelector('header', { timeout: 10000 })
  })

  test('should show hamburger menu on mobile viewport', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    
    // Check if hamburger menu is visible
    const hamburgerButton = page.locator('button[aria-label="Toggle sidebar"]')
    await expect(hamburgerButton).toBeVisible()
    
    // Check if sidebar is initially hidden on mobile
    const sidebar = page.locator('.w-80').first()
    await expect(sidebar).toHaveClass(/translate-x-full|hidden/)
  })

  test('should toggle sidebar on mobile when hamburger is clicked', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    
    // Click hamburger menu
    const hamburgerButton = page.locator('button[aria-label="Toggle sidebar"]')
    await hamburgerButton.click()
    
    // Check if sidebar becomes visible
    const sidebar = page.locator('.w-80').first()
    await expect(sidebar).toHaveClass(/translate-x-0/)
    
    // Check if backdrop is visible
    const backdrop = page.locator('.bg-black.bg-opacity-50')
    await expect(backdrop).toBeVisible()
  })

  test('should auto-close sidebar after group selection on mobile', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    
    // Open sidebar
    const hamburgerButton = page.locator('button[aria-label="Toggle sidebar"]')
    await hamburgerButton.click()
    
    // Wait for sidebar to be visible
    const sidebar = page.locator('.w-80').first()
    await expect(sidebar).toHaveClass(/translate-x-0/)
    
    // Click on a group
    const firstGroup = page.locator('[data-testid="group-item"]').first()
    if (await firstGroup.count() > 0) {
      await firstGroup.click()
      
      // Check if sidebar auto-closes
      await expect(sidebar).toHaveClass(/translate-x-full/)
    }
  })

  test('should show mobile-optimized chat interface', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    
    // Select a group first
    const hamburgerButton = page.locator('button[aria-label="Toggle sidebar"]')
    await hamburgerButton.click()
    
    const firstGroup = page.locator('[data-testid="group-item"]').first()
    if (await firstGroup.count() > 0) {
      await firstGroup.click()
      
      // Check if chat interface uses full width
      const chatContainer = page.locator('[data-testid="messages-container"]')
      await expect(chatContainer).toBeVisible()
      
      // Check if message input has mobile optimizations
      const messageInput = page.locator('input[placeholder="Type a message..."]')
      await expect(messageInput).toHaveClass(/text-sm/)
      
      // Check if send button has mobile icon
      const sendButton = page.locator('button[type="submit"]')
      await expect(sendButton).toHaveClass(/min-h-\[44px\]/)
    }
  })

  test('should show mobile-friendly reply buttons', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    
    // Navigate to a group with messages
    const hamburgerButton = page.locator('button[aria-label="Toggle sidebar"]')
    await hamburgerButton.click()
    
    const firstGroup = page.locator('[data-testid="group-item"]').first()
    if (await firstGroup.count() > 0) {
      await firstGroup.click()
      
      // Check if reply buttons are visible and properly sized for mobile
      const replyButtons = page.locator('[data-testid="reply-button"]')
      if (await replyButtons.count() > 0) {
        const firstReplyButton = replyButtons.first()
        await expect(firstReplyButton).toBeVisible()
        
        // Check if button has mobile-friendly size
        await expect(firstReplyButton).toHaveClass(/w-12.*h-12/)
      }
    }
  })

  test('should handle different mobile viewport sizes', async ({ page }) => {
    const viewports = [
      { width: 320, height: 568 }, // iPhone SE
      { width: 375, height: 667 }, // iPhone 8
      { width: 414, height: 896 }, // iPhone 11
    ]

    for (const viewport of viewports) {
      await page.setViewportSize(viewport)
      
      // Check if hamburger menu is visible
      const hamburgerButton = page.locator('button[aria-label="Toggle sidebar"]')
      await expect(hamburgerButton).toBeVisible()
      
      // Check if layout adapts properly
      const header = page.locator('header')
      await expect(header).toBeVisible()
      
      // Check if content area is properly sized
      const mainContent = page.locator('.flex-1.flex').first()
      await expect(mainContent).toBeVisible()
    }
  })

  test('should show mobile back button in group detail', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    
    // Select a group
    const hamburgerButton = page.locator('button[aria-label="Toggle sidebar"]')
    await hamburgerButton.click()
    
    const firstGroup = page.locator('[data-testid="group-item"]').first()
    if (await firstGroup.count() > 0) {
      await firstGroup.click()
      
      // Check if mobile back button is visible in group detail
      const backButton = page.locator('button[aria-label="Open groups"]')
      await expect(backButton).toBeVisible()
    }
  })

  test('should have touch-friendly tab navigation', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    
    // Navigate to a group
    const hamburgerButton = page.locator('button[aria-label="Toggle sidebar"]')
    await hamburgerButton.click()
    
    const firstGroup = page.locator('[data-testid="group-item"]').first()
    if (await firstGroup.count() > 0) {
      await firstGroup.click()
      
      // Check if tabs are properly spaced for mobile
      const tabs = page.locator('nav button')
      const chatTab = tabs.filter({ hasText: 'Chat' })
      const membersTab = tabs.filter({ hasText: 'Members' })
      
      await expect(chatTab).toBeVisible()
      await expect(membersTab).toBeVisible()
      
      // Test tab switching
      await membersTab.click()
      await expect(membersTab).toHaveClass(/border-blue-500/)
    }
  })

  test('should close sidebar when backdrop is clicked', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    
    // Open sidebar
    const hamburgerButton = page.locator('button[aria-label="Toggle sidebar"]')
    await hamburgerButton.click()
    
    // Wait for sidebar and backdrop to be visible
    const backdrop = page.locator('.bg-black.bg-opacity-50')
    await expect(backdrop).toBeVisible()
    
    // Click on the right side of the backdrop (away from sidebar)
    await page.click('body', { position: { x: 350, y: 200 } })

    // Wait for backdrop to disappear (indicates sidebar closed)
    await expect(backdrop).not.toBeVisible()

    // Alternative check: verify sidebar is not visible or has closed state
    const sidebar = page.locator('.w-80').first()
    await expect(sidebar).toHaveClass(/translate-x-full|hidden/)
  })
})
